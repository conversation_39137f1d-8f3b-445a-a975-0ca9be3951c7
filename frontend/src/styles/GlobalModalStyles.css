/* 全局弹窗样式 */

/* 1. 所有弹窗居中 */
.ant-modal-wrap {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.ant-modal {
  top: 0 !important;
  margin: 0 auto !important;
  padding-bottom: 0 !important;
  width: 400px !important; /* 统一宽度 */
}

/* 试用期提醒弹窗例外 - 使用更大宽度 */
.probation-reminder-modal .ant-modal,
.probation-regularization-modal .ant-modal {
  width: auto !important; /* 让专门的CSS文件控制宽度 */
}

/* 试用期提醒弹窗按钮布局例外 */
.probation-reminder-modal .ant-modal-footer {
  text-align: center !important;
  padding: 16px 24px !important;
}

.probation-reminder-modal .ant-modal-footer .ant-btn {
  margin: 0 6px !important;
}

/* 转正确认弹窗按钮布局例外 - 靠右对齐 */
.probation-regularization-modal .ant-modal-footer {
  padding: 16px 24px !important;
  text-align: right !important;
}

.probation-regularization-modal .ant-modal-footer > div {
  display: flex !important;
  justify-content: flex-end !important;
  gap: 8px !important;
}

.ant-modal-content {
  border-radius: 8px !important;
  overflow: hidden !important;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1) !important;
}

/* 2. 弹窗标题居中 */
.ant-modal-header {
  padding: 16px 24px !important;
  text-align: center !important;
  border-bottom: 1px solid #f0f0f0 !important;
}

.ant-modal-title {
  font-size: 16px !important;
  font-weight: 600 !important;
  color: #333 !important;
  text-align: center !important;
}

/* 3. 弹窗内容样式 */
.ant-modal-body {
  padding: 24px !important;
}

/* 4. 弹窗底部按钮样式 */
.ant-modal-footer {
  border-top: 1px solid #f0f0f0 !important;
  padding: 10px 16px !important;
  text-align: center !important; /* 按钮居中 */
}

/* 5. 确认弹窗样式 */
.ant-modal-confirm .ant-modal-body {
  padding: 24px 24px 16px !important;
}

.ant-modal-confirm-title {
  font-size: 16px !important;
  font-weight: 600 !important;
  color: #333 !important;
  text-align: center !important;
  margin-bottom: 8px !important;
}

.ant-modal-confirm-content {
  margin-top: 16px !important;
  margin-bottom: 16px !important;
  font-size: 14px !important;
  color: #666 !important;
  text-align: center !important;
}

/* 6. 确认弹窗按钮样式 */
.ant-modal-confirm-btns {
  display: flex !important;
  justify-content: center !important; /* 按钮居中 */
  margin-top: 24px !important;
  gap: 12px !important;
}

.ant-modal-confirm-btns .ant-btn {
  min-width: 80px !important; /* 按钮最小宽度 */
  height: 36px !important;
  padding: 0 16px !important;
  font-size: 14px !important;
  border-radius: 4px !important;
}

.ant-modal-confirm-btns .ant-btn-primary {
  background-color: #1890ff !important;
  border-color: #1890ff !important;
}

.ant-modal-confirm-btns .ant-btn-primary:hover {
  background-color: #40a9ff !important;
  border-color: #40a9ff !important;
}

.ant-modal-confirm-btns .ant-btn-default {
  background-color: #fff !important;
  border-color: #d9d9d9 !important;
  color: rgba(0, 0, 0, 0.65) !important;
}

.ant-modal-confirm-btns .ant-btn-default:hover {
  border-color: #40a9ff !important;
  color: #40a9ff !important;
}

.ant-modal-confirm-btns .ant-btn-dangerous {
  background-color: #ff4d4f !important;
  border-color: #ff4d4f !important;
  color: #fff !important;
}

.ant-modal-confirm-btns .ant-btn-dangerous:hover {
  background-color: #ff7875 !important;
  border-color: #ff7875 !important;
}

/* 7. Popconfirm 弹窗样式 */
.ant-popover {
  z-index: 1050 !important;
}

.ant-popover-inner {
  border-radius: 8px !important;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1) !important;
}

.ant-popover-inner-content {
  padding: 16px !important;
}

.ant-popover-message {
  padding: 8px 0 16px !important;
  font-size: 14px !important;
  color: #333 !important;
  text-align: center !important;
}

.ant-popover-message-title {
  padding-left: 0 !important;
  text-align: center !important;
}

.ant-popover-buttons {
  display: flex !important;
  justify-content: center !important;
  gap: 12px !important;
}

.ant-popover-buttons .ant-btn {
  min-width: 80px !important;
  height: 32px !important;
  padding: 0 16px !important;
  font-size: 14px !important;
  border-radius: 4px !important;
}

/* 8. 管理后台特定样式 */
.admin-page-container .ant-modal,
.admin-container .ant-modal {
  width: 400px !important;
}

.admin-page-container .ant-modal-content,
.admin-container .ant-modal-content {
  width: 400px !important;
}

/* 9. 用户管理和数据库管理的表单按钮布局 */
.user-form-actions,
.database-form-actions {
  display: flex !important;
  flex-direction: row !important;
  flex-wrap: nowrap !important;
  justify-content: center !important;
  gap: 12px !important;
  margin-top: 24px !important;
}

.user-form-actions button,
.database-form-actions button {
  min-width: 80px !important;
  height: 36px !important;
  padding: 0 16px !important;
  font-size: 14px !important;
  border-radius: 4px !important;
}

/* 10. 删除确认弹窗样式 */
.delete-confirm-modal .ant-modal-content {
  width: 400px !important;
}

.delete-confirm-modal .ant-modal-body {
  padding: 24px !important;
}

.delete-confirm-title {
  color: #ff4d4f !important;
  font-weight: bold !important;
  font-size: 18px !important;
  text-align: center !important;
  margin-bottom: 16px !important;
}

.delete-confirm-content {
  color: #333 !important;
  font-size: 14px !important;
  text-align: center !important;
  margin-bottom: 24px !important;
}

.delete-confirm-footer {
  display: flex !important;
  justify-content: center !important;
  gap: 12px !important;
}

.delete-confirm-cancel-btn {
  min-width: 80px !important;
  height: 36px !important;
  padding: 0 16px !important;
  border: 1px solid #d9d9d9 !important;
  border-radius: 4px !important;
  background: #fff !important;
  color: rgba(0, 0, 0, 0.65) !important;
  font-size: 14px !important;
  cursor: pointer !important;
  transition: all 0.3s !important;
}

.delete-confirm-confirm-btn {
  min-width: 80px !important;
  height: 36px !important;
  padding: 0 16px !important;
  border: 1px solid #ff4d4f !important;
  border-radius: 4px !important;
  background: #ff4d4f !important;
  color: #fff !important;
  font-size: 14px !important;
  cursor: pointer !important;
  transition: all 0.3s !important;
}

.delete-confirm-cancel-btn:hover {
  border-color: #40a9ff !important;
  color: #40a9ff !important;
}

.delete-confirm-confirm-btn:hover {
  background: #ff7875 !important;
  border-color: #ff7875 !important;
}
