/* 试用期提醒弹窗样式 */

/* 主弹窗样式 */
.probation-reminder-modal .ant-modal {
  width: 800px !important;
  max-width: 95vw !important;
}

.probation-reminder-modal .ant-modal-content {
  width: 800px !important;
  max-width: 95vw !important;
}

.probation-reminder-modal .ant-modal-body {
  padding: 24px !important;
  max-height: 80vh !important;
  overflow-y: auto !important;
}

/* 表格样式优化 */
.probation-reminder-modal .ant-table {
  font-size: 14px !important;
}

.probation-reminder-modal .ant-table-thead > tr > th {
  background-color: #fafafa !important;
  font-weight: 600 !important;
  text-align: center !important;
  padding: 12px 8px !important;
  white-space: nowrap !important;
}

.probation-reminder-modal .ant-table-tbody > tr > td {
  padding: 12px 8px !important;
  text-align: center !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

/* 状态标签样式 */
.probation-reminder-modal .ant-tag {
  margin: 0 !important;
  padding: 4px 8px !important;
  border-radius: 4px !important;
  font-size: 12px !important;
  font-weight: 500 !important;
  white-space: nowrap !important;
}

/* 操作按钮样式 */
.probation-reminder-modal .ant-btn-primary {
  background-color: #1890ff !important;
  border-color: #1890ff !important;
  height: 28px !important;
  padding: 0 12px !important;
  font-size: 12px !important;
  border-radius: 4px !important;
}

.probation-reminder-modal .ant-btn-primary:hover {
  background-color: #40a9ff !important;
  border-color: #40a9ff !important;
}

/* 底部按钮样式 */
.probation-reminder-modal .ant-modal-footer {
  text-align: center !important;
  padding: 16px 24px !important;
  border-top: 1px solid #f0f0f0 !important;
}

.probation-reminder-close-btn {
  min-width: 80px !important;
  height: 36px !important;
  padding: 0 16px !important;
  font-size: 14px !important;
  border-radius: 4px !important;
  background-color: #fff !important;
  border-color: #d9d9d9 !important;
  color: rgba(0, 0, 0, 0.65) !important;
}

.probation-reminder-close-btn:hover {
  border-color: #40a9ff !important;
  color: #40a9ff !important;
}

/* 转正确认弹窗样式 */
.probation-regularization-modal .ant-modal {
  width: 600px !important;
  max-width: 90vw !important;
}

.probation-regularization-modal .ant-modal-content {
  width: 600px !important;
  max-width: 90vw !important;
}

.probation-regularization-modal .ant-modal-body {
  padding: 24px !important;
}

/* 员工信息展示区域 */
.probation-regularization-modal .ant-modal-body > div:first-child {
  margin-bottom: 16px !important;
  padding: 12px !important;
  background-color: #f5f5f5 !important;
  border-radius: 4px !important;
}

.probation-regularization-modal .ant-modal-body > div:first-child p {
  margin: 4px 0 !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
}

/* 表单样式 */
.probation-regularization-modal .ant-form-item {
  margin-bottom: 20px !important;
}

.probation-regularization-modal .ant-form-item-label {
  padding-bottom: 4px !important;
}

.probation-regularization-modal .ant-form-item-label > label {
  font-size: 14px !important;
  font-weight: 500 !important;
  color: #333 !important;
}

/* 一行三列布局样式 */
.probation-regularization-modal .ant-form-item[style*="flex: 1"] {
  margin-bottom: 20px !important;
}

.probation-regularization-modal .ant-form-item[style*="flex: 1"] .ant-form-item-label {
  white-space: nowrap !important;
}

.probation-regularization-modal .ant-input,
.probation-regularization-modal .ant-select-selector,
.probation-regularization-modal .ant-picker {
  height: 36px !important;
  border-radius: 4px !important;
  font-size: 14px !important;
}

.probation-regularization-modal .ant-input:focus,
.probation-regularization-modal .ant-select-focused .ant-select-selector,
.probation-regularization-modal .ant-picker:hover,
.probation-regularization-modal .ant-picker-focused {
  border-color: #40a9ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

/* 文本域样式 */
.probation-regularization-modal .ant-input {
  resize: vertical !important;
  min-height: 36px !important;
}

/* 备注字数提醒样式 */
.probation-regularization-modal .ant-form-item:last-child div[style*="textAlign: 'right'"] {
  margin-top: 4px !important;
  font-size: 12px !important;
  color: #999 !important;
  text-align: right !important;
}

/* 底部按钮样式 */
.probation-regularization-modal .ant-modal-footer {
  text-align: center !important;
  padding: 16px 24px !important;
  border-top: 1px solid #f0f0f0 !important;
}

.probation-regularization-modal .ant-modal-footer .ant-btn {
  min-width: 80px !important;
  height: 36px !important;
  padding: 0 16px !important;
  font-size: 14px !important;
  border-radius: 4px !important;
  margin: 0 6px !important;
}

.probation-regularization-modal .ant-modal-footer .ant-btn-default {
  background-color: #fff !important;
  border-color: #d9d9d9 !important;
  color: rgba(0, 0, 0, 0.65) !important;
}

.probation-regularization-modal .ant-modal-footer .ant-btn-default:hover {
  border-color: #40a9ff !important;
  color: #40a9ff !important;
}

.probation-regularization-modal .ant-modal-footer .ant-btn-primary {
  background-color: #1890ff !important;
  border-color: #1890ff !important;
  color: #fff !important;
}

.probation-regularization-modal .ant-modal-footer .ant-btn-primary:hover {
  background-color: #40a9ff !important;
  border-color: #40a9ff !important;
}

.probation-regularization-modal .ant-modal-footer .ant-btn-primary[disabled] {
  background-color: #f5f5f5 !important;
  border-color: #d9d9d9 !important;
  color: rgba(0, 0, 0, 0.25) !important;
  cursor: not-allowed !important;
}

/* 加载状态样式 */
.probation-regularization-modal .ant-btn-loading {
  pointer-events: none !important;
}

/* 响应式设计 */
@media (max-width: 1500px) {
  .probation-reminder-modal .ant-modal,
  .probation-reminder-modal .ant-modal-content {
    width: 1200px !important;
  }
}

@media (max-width: 1300px) {
  .probation-reminder-modal .ant-modal,
  .probation-reminder-modal .ant-modal-content {
    width: 95vw !important;
  }
}

@media (max-width: 768px) {
  .probation-reminder-modal .ant-modal,
  .probation-reminder-modal .ant-modal-content {
    width: 95vw !important;
    margin: 0 auto !important;
  }
  
  .probation-regularization-modal .ant-modal,
  .probation-regularization-modal .ant-modal-content {
    width: 95vw !important;
    margin: 0 auto !important;
  }
}

/* 确保弹窗在屏幕中央 */
.probation-reminder-modal .ant-modal-wrap,
.probation-regularization-modal .ant-modal-wrap {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.probation-reminder-modal .ant-modal,
.probation-regularization-modal .ant-modal {
  top: 0 !important;
  margin: 0 auto !important;
  padding-bottom: 0 !important;
}
